import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Minus, ShoppingCart, ArrowLeft } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { InputDateTime, InputTextArea } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { GetAllMenu } from '@/api/cafeteria/menu';
import SpecialOrdersTable from './SpecialOrdersTable';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface OrderSpecialProps {
  onBack: () => void;
  mutate?: () => void;
}

export default function OrderSpecial({ onBack, mutate }: OrderSpecialProps) {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showNewOrder, setShowNewOrder] = useState(false);

  const form = useForm({
    defaultValues: {
      neededDate: '',
      purpose: '',
    },
    mode: 'onChange',
  });

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const { menu, menuLoading } = GetAllMenu('?page=1&limit=1000');
  const menuItems = menu?.data?.menu || [];

  const addToCart = (item: any) => {
    setCart((prev) => {
      const existing = prev.find((cartItem) => cartItem.id === item.id);
      if (existing) {
        return prev.map((cartItem) =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      }
      return [
        ...prev,
        {
          id: item.id,
          name: item.name,
          price: item.generalPrice,
          quantity: 1,
        },
      ];
    });
  };

  const updateQuantity = (id: string, change: number) => {
    setCart((prev) => {
      return prev
        .map((item) => {
          if (item.id === id) {
            const newQuantity = Math.max(0, item.quantity + change);
            return newQuantity === 0
              ? null
              : { ...item, quantity: newQuantity };
          }
          return item;
        })
        .filter(Boolean) as CartItem[];
    });
  };

  const setQuantityDirect = (id: string, quantity: number) => {
    const qty = Math.max(0, quantity);
    setCart((prev) => {
      return prev
        .map((item) => {
          if (item.id === id) {
            return qty === 0 ? null : { ...item, quantity: qty };
          }
          return item;
        })
        .filter(Boolean) as CartItem[];
    });
  };

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + item.price * item.quantity, 0);
  };

  const handleSubmit = async () => {
    if (cart.length === 0) {
      toast.error('Please add items to cart');
      return;
    }

    const { neededDate, purpose } = form.getValues();

    const payload = {
      cart: cart,
      neededBy: neededDate,
      purpose: purpose.trim(),
      totalAmount: getTotalAmount(),
    };

    try {
      setIsLoading(true);
      const res = await myApi.post(
        '/cafeteria/orders/new-special-order',
        payload
      );

      if (res.status === 200) {
        toast.success(res.data.data.message);
        setCart([]);
        form.reset();
        if (mutate) mutate();
        onBack();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  if (showNewOrder) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowNewOrder(false)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Special Orders
          </Button>
        </div>
        <div>
          <h2 className="text-xl font-bold">New Special Order</h2>
          <p className="text-sm font-medium text-red-500">
            Please note: Special orders should be placed at least 24 hours in
            advance to allow sufficient time for approval and cafeteria
            preparations.
          </p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Menu Items */}
          <div>
            <h3 className="font-semibold mb-4">Menu Items</h3>
            {menuLoading ? (
              <div className="text-center py-8">Loading menu...</div>
            ) : (
              <div className="space-y-2 max-h-[600px] overflow-y-auto">
                {menuItems.map((item: any) => {
                  const isSelected = cart.some(
                    (cartItem) => cartItem.id === item.id
                  );
                  return (
                    <div
                      key={item.id}
                      className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 cursor-pointer"
                      onClick={() => {
                        if (isSelected) {
                          setCart((prev) =>
                            prev.filter((cartItem) => cartItem.id !== item.id)
                          );
                        } else {
                          addToCart(item);
                        }
                      }}
                    >
                      <Checkbox checked={isSelected} />
                      <span className="font-medium text-sm">{item.name}</span>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Cart */}
          <div>
            <h3 className="font-semibold mb-4 flex items-center">
              <ShoppingCart className="w-4 h-4 mr-2" />
              Cart ({cart.length})
            </h3>

            <div className="space-y-4 max-h-[400px] overflow-y-auto mb-4">
              {cart.length === 0 ? (
                <p className="text-gray-500 text-center py-8">Cart is empty</p>
              ) : (
                cart.map((item) => (
                  <div key={item.id} className="border rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-sm">{item.name}</h4>
                      <span className="text-sm font-semibold">
                        ₦{(item.price * item.quantity).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.id, -1)}
                        >
                          <Minus className="w-3 h-3" />
                        </Button>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) =>
                            setQuantityDirect(
                              item.id,
                              parseInt(e.target.value) || 0
                            )
                          }
                          className="w-16 text-center"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.id, 1)}
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </div>
                      <span className="text-xs text-gray-500">
                        ₦{item.price.toLocaleString()} each
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>

            <Form {...form}>
              <div className="space-y-4">
                <InputDateTime
                  control={form.control}
                  name="neededDate"
                  label="Date & Time Needed *"
                  placeholder="Select date and time needed"
                  minDate={today}
                />

                <InputTextArea
                  control={form.control}
                  name="purpose"
                  label="Purpose *"
                  placeholder="Enter purpose for this special order..."
                />
              </div>
            </Form>

            {/* Total */}
            <div className="border-t pt-4 mb-4">
              <div className="flex justify-between items-center text-lg font-semibold">
                <span>Total:</span>
                <span>₦{getTotalAmount().toLocaleString()}</span>
              </div>
            </div>

            {/* Actions */}
            <Button
              onClick={handleSubmit}
              disabled={
                isLoading ||
                cart.length === 0 ||
                !form.watch('neededDate') ||
                !form.watch('purpose')?.trim()
              }
              className="w-full"
            >
              {isLoading ? 'Placing...' : 'Place Order'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Orders
        </Button>
      </div>

      <SpecialOrdersTable onNewOrder={() => setShowNewOrder(true)} />
    </div>
  );
}
