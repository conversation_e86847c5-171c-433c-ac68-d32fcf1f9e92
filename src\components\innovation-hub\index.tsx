'use client';

import React, { useState } from 'react';
import { Lightbulb } from 'lucide-react';
import { toast } from 'sonner';
import { PERMISSIONS, hasPermission } from '@/lib/types/permissions';
import {
  Idea,
  IdeaComment,
  IdeaFormValues,
  IdeaStatus,
  initialIdeas,
} from './components/types';
import { IdeaCard } from './components/idea-card';
import { CommentModal } from './components/comment-modal';
import { SubmitIdeaForm } from './components/submit-idea-form';


export default function InnovationHubContent() {
  const [ideas, setIdeas] = useState<Idea[]>(initialIdeas);
  const [isCommentModalOpen, setCommentModalOpen] = useState(false);
  const [selectedIdeaForComments, setSelectedIdeaForComments] =
    useState<Idea | null>(null);
  const canEdit = hasPermission(PERMISSIONS.HUB_EDIT);

  const handleIdeaSubmit = (values: IdeaFormValues) => {
    const newIdea: Idea = {
      id: (ideas.length + 1).toString(),
      ...values,
      author: {
        name: 'Current User', // This would be dynamic in a real app
        avatar: 'https://github.com/radix-ui.png',
      },
      createdAt: 'Just now',
      likes: 0,
      comments: [],
      status: 'New' as const,
    };
    setIdeas([newIdea, ...ideas]);
  };

  const handleLike = (id: string) => {
    setIdeas(
      ideas.map((idea) =>
        idea.id === id ? { ...idea, likes: idea.likes + 1 } : idea
      )
    );
  };

  const handleDelete = (id: string) => {
    setIdeas(ideas.filter((idea) => idea.id !== id));
    toast.warning('Idea has been deleted.');
  };

  const handleStatusUpdate = (id: string, newStatus: IdeaStatus) => {
    setIdeas(
      ideas.map((idea: any) =>
        idea.id === id ? { ...idea, status: newStatus } : idea
      )
    );
    toast.success(`Idea status updated to "${newStatus}".`);
  };

  const handleOpenCommentModal = (idea: Idea) => {
    setSelectedIdeaForComments(idea);
    setCommentModalOpen(true);
  };

  const handleCommentSubmit = (commentText: string) => {
    if (!selectedIdeaForComments) return;

    const newComment: IdeaComment = {
      id: `c${Date.now()}`,
      author: {
        name: 'Current User', // This would be dynamic in a real app
        avatar: 'https://github.com/radix-ui.png',
      },
      text: commentText,
      createdAt: 'Just now',
    };

    setIdeas(
      ideas.map((idea: any) =>
        idea.id === selectedIdeaForComments.id
          ? { ...idea, comments: [...idea.comments, newComment] }
          : idea
      )
    );
    setCommentModalOpen(false);
    toast.success('Your comment has been added.');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <Lightbulb className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Innovation Hub
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Explore, share, and collaborate on cutting-edge ideas.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Submit Idea Form */}
        <div className="lg:col-span-1">
          <SubmitIdeaForm onIdeaSubmit={handleIdeaSubmit} />
        </div>

        {/* Ideas Feed */}
        <div className="lg:col-span-2 space-y-6">
          <h2 className="text-xl font-semibold">Latest Ideas</h2>
          {ideas.map((idea) => (
            <IdeaCard
              key={idea.id}
              idea={idea}
              onLike={handleLike}
              canEdit={canEdit}
              onDelete={handleDelete}
              onStatusUpdate={handleStatusUpdate}
              onCommentClick={handleOpenCommentModal}
            />
          ))}
        </div>
      </div>
      <CommentModal
        idea={selectedIdeaForComments}
        open={isCommentModalOpen}
        onOpenChange={setCommentModalOpen}
        onCommentSubmit={handleCommentSubmit}
      />
    </div>
  );
}
