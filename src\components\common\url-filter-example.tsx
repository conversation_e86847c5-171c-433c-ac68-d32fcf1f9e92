'use client';

import React from 'react';
import { useUrlSearchAndPagination } from '@/hooks/useUrlSearchAndPagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import DateRangeFilter from '@/components/common/date-range-filter';
import { Search } from 'lucide-react';

/**
 * Example component demonstrating URL-based filtering functionality
 * This component shows how filters appear in URLs and can be bookmarked/shared
 */
const UrlFilterExample = () => {
  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    handlePageChange,
    filters,
    updateFilter,
    clearAllFilters,
    queryParam,
  } = useUrlSearchAndPagination({ initialPageSize: 10 });

  // Get filter values from URL state
  const statusFilter = (filters.status as string) || '';
  const categoryFilter = (filters.category as string) || '';
  const startDate = filters.startDate as Date | undefined;
  const endDate = filters.endDate as Date | undefined;

  const handleStatusChange = (value: string) => {
    updateFilter('status', value === 'all' ? undefined : value);
  };

  const handleCategoryChange = (value: string) => {
    updateFilter('category', value === 'all' ? undefined : value);
  };

  const handleDateRangeChange = (start: Date | null, end: Date | null) => {
    updateFilter('startDate', start);
    updateFilter('endDate', end);
  };

  // Mock data for demonstration
  const mockData = [
    { id: 1, name: 'Item 1', status: 'active', category: 'electronics', date: '2024-01-15' },
    { id: 2, name: 'Item 2', status: 'inactive', category: 'clothing', date: '2024-01-20' },
    { id: 3, name: 'Item 3', status: 'pending', category: 'electronics', date: '2024-01-25' },
    { id: 4, name: 'Item 4', status: 'active', category: 'books', date: '2024-02-01' },
    { id: 5, name: 'Item 5', status: 'inactive', category: 'clothing', date: '2024-02-05' },
  ];

  // Filter mock data based on current filters
  const filteredData = mockData.filter(item => {
    const matchesSearch = !searchTerm || item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || item.status === statusFilter;
    const matchesCategory = !categoryFilter || item.category === categoryFilter;
    
    let matchesDateRange = true;
    if (startDate || endDate) {
      const itemDate = new Date(item.date);
      if (startDate && itemDate < startDate) matchesDateRange = false;
      if (endDate && itemDate > endDate) matchesDateRange = false;
    }
    
    return matchesSearch && matchesStatus && matchesCategory && matchesDateRange;
  });

  return (
    <div className="p-6 space-y-6">
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">URL-Based Filtering Demo</h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          This component demonstrates URL-based filtering. All filter states are reflected in the URL,
          making them bookmarkable and shareable. Try applying filters and notice how the URL changes.
        </p>
      </div>

      {/* Current URL State Display */}
      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h3 className="font-medium mb-2">Current URL State:</h3>
        <div className="text-sm font-mono bg-white dark:bg-gray-900 p-2 rounded border">
          {window.location.pathname}
          {queryParam || searchTerm || currentPage > 1 ? '?' : ''}
          {[
            searchTerm && `search=${encodeURIComponent(searchTerm)}`,
            currentPage > 1 && `page=${currentPage}`,
            queryParam
          ].filter(Boolean).join('&')}
        </div>
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search items..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>

        {/* Status Filter */}
        <Select value={statusFilter || 'all'} onValueChange={handleStatusChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>

        {/* Category Filter */}
        <Select value={categoryFilter || 'all'} onValueChange={handleCategoryChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="electronics">Electronics</SelectItem>
            <SelectItem value="clothing">Clothing</SelectItem>
            <SelectItem value="books">Books</SelectItem>
          </SelectContent>
        </Select>

        {/* Date Range Filter */}
        <DateRangeFilter
          onFilterChange={handleDateRangeChange}
          initialStartDate={startDate}
          initialEndDate={endDate}
          className="w-full"
        />
      </div>

      {/* Clear Filters Button */}
      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={clearAllFilters}>
          Clear All Filters
        </Button>
        <span className="text-sm text-gray-500">
          Showing {filteredData.length} of {mockData.length} items
        </span>
      </div>

      {/* Data Table */}
      <div className="border rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th className="px-4 py-2 text-left">ID</th>
              <th className="px-4 py-2 text-left">Name</th>
              <th className="px-4 py-2 text-left">Status</th>
              <th className="px-4 py-2 text-left">Category</th>
              <th className="px-4 py-2 text-left">Date</th>
            </tr>
          </thead>
          <tbody>
            {filteredData.length > 0 ? (
              filteredData.map((item) => (
                <tr key={item.id} className="border-t">
                  <td className="px-4 py-2">{item.id}</td>
                  <td className="px-4 py-2">{item.name}</td>
                  <td className="px-4 py-2">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      item.status === 'active' ? 'bg-green-100 text-green-800' :
                      item.status === 'inactive' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="px-4 py-2 capitalize">{item.category}</td>
                  <td className="px-4 py-2">{item.date}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-4 py-8 text-center text-gray-500">
                  No items match the current filters
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Example */}
      <div className="flex justify-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage <= 1}
        >
          Previous
        </Button>
        <span className="px-3 py-1 text-sm">
          Page {currentPage}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
        >
          Next
        </Button>
      </div>

      {/* Instructions */}
      <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
        <h3 className="font-medium mb-2">Testing Instructions:</h3>
        <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-400">
          <li>• Apply different filters and notice how the URL changes</li>
          <li>• Copy the URL and paste it in a new tab - the filters should be preserved</li>
          <li>• Use browser back/forward buttons - filter state should be maintained</li>
          <li>• Share the URL with filters applied - others will see the same filtered view</li>
          <li>• Bookmark a filtered page and return to it later</li>
        </ul>
      </div>
    </div>
  );
};

export default UrlFilterExample;
