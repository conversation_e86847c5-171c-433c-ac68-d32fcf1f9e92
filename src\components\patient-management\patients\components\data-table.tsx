'use client';

import React, { useState } from 'react';
import { useUrlSearchAndPagination } from '@/hooks/useUrlSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search, User } from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';
import { GetPatients } from '@/api/crm/data';
import { Patient } from '@/components/crm/types';
import Details from './details';
import Create from './create';

interface PatientTableProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function PatientTable({
  openCreate,
  setOpenCreate,
}: PatientTableProps) {
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    filters,
    updateFilter,
  } = useUrlSearchAndPagination({ initialPageSize: 10 });

  const [openDetails, setOpenDetails] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);

  // Get filter values from URL state
  const statusFilter = (filters.status as string) || '';
  const startDate = filters.startDate as Date | null;
  const endDate = filters.endDate as Date | null;

  const handleEventFromModal = (patient: Patient) => {
    setSelectedPatient(patient);
    setOpenDetails(true);
  };

  // No need for manual query parameter building - the hook handles this automatically

  const { patients, patientsLoading, mutate } = GetPatients(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );
  const patientData = patients?.data?.patients;
  const totalPages = patients?.data?.totalPages ?? 0;

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search patients..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="flex gap-2">
            <button
              onClick={() => updateFilter('status', 'active')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'active'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Active
            </button>
            <button
              onClick={() => updateFilter('status', 'inactive')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'inactive'
                  ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Inactive
            </button>
            <button
              onClick={() => updateFilter('status', undefined)}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === ''
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              All
            </button>
          </div>
          <DateRangeFilter
            onFilterChange={(start, end) => {
              updateFilter('startDate', start);
              updateFilter('endDate', end);
            }}
            initialStartDate={startDate}
            initialEndDate={endDate}
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100 dark:bg-[#1F1F23] text-xs text-gray-500 dark:text-gray-400">
              <th className="table-style">#</th>
              <th className="table-style">Patient ID</th>
              <th className="table-style">Name</th>
              <th className="table-style">Gender</th>
              <th className="table-style">Age</th>
              <th className="table-style">Phone</th>
              <th className="table-style">Blood Type</th>
              <th className="table-style">Last Visit</th>
              <th className="table-style">Status</th>
              <th className="table-style">Actions</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {patientsLoading ? (
              <tr>
                <td colSpan={10} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading patients...</span>
                  </div>
                </td>
              </tr>
            ) : patientData && patientData.length > 0 ? (
              patientData.map((patient: Patient, index: number) => {
                // Calculate age from date of birth
                const age = patient.dateOfBirth
                  ? dayjs().diff(dayjs(patient.dateOfBirth), 'year')
                  : 'N/A';

                return (
                  <tr
                    className="text-xs text-[#062A55] dark:text-white"
                    key={patient.id}
                  >
                    <td className="table-style">
                      {currentPage === 1
                        ? index + 1
                        : (currentPage - 1) * pageSize + (index + 1)}
                    </td>
                    <td className="table-style">
                      P-{String(patient.id).padStart(6, '0')}
                    </td>
                    <td className="table-style">
                      {patient.title ? `${patient.title} ` : ''}
                      {patient.firstName} {patient.lastName}
                    </td>
                    <td className="table-style">
                      {patient.gender
                        ? patient.gender.charAt(0).toUpperCase() +
                          patient.gender.slice(1)
                        : 'N/A'}
                    </td>
                    <td className="table-style">{age}</td>
                    <td className="table-style">{patient.phoneNumber}</td>
                    <td className="table-style">
                      {patient.bloodType || 'N/A'}
                    </td>
                    <td className="table-style">
                      {patient.lastVisitDate
                        ? dayjs(patient.lastVisitDate).format('MMM D, YYYY')
                        : 'Never'}
                    </td>
                    <td className="table-style">
                      <StatusBadge status={patient.status} />
                    </td>
                    <td className="table-style">
                      <Ellipsis
                        onClick={() => handleEventFromModal(patient)}
                        className="w-4 h-4 cursor-pointer"
                      />
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={10} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <User className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No patients found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Patient records will appear here once they are added'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        <div className="mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      </div>

      {selectedPatient && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          data={selectedPatient}
          mutate={mutate}
        />
      )}

      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </div>
  );
}
