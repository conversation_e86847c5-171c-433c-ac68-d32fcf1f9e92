import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';

interface UseUrlSearchAndPaginationProps {
  initialPageSize?: number;
  debounceDelay?: number;
}

interface FilterState {
  [key: string]: string | number | boolean | Date | undefined | null;
}

interface UseUrlSearchAndPaginationReturn {
  // Search state
  searchTerm: string;
  debouncedSearchTerm: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;

  // Pagination state
  currentPage: number;
  pageSize: number;
  handlePageChange: (pageNumber: number) => void;

  // Filter state management
  filters: FilterState;
  updateFilter: (key: string, value: string | number | boolean | Date | undefined | null) => void;
  updateFilters: (newFilters: Partial<FilterState>) => void;
  clearFilter: (key: string) => void;
  clearAllFilters: () => void;

  // Query building (for API calls)
  queryParam: string;
  
  // URL state management
  updateUrl: (newFilters?: Partial<FilterState>, resetPage?: boolean) => void;
}

export function useUrlSearchAndPagination({
  initialPageSize = 15,
  debounceDelay = 500,
}: UseUrlSearchAndPaginationProps = {}): UseUrlSearchAndPaginationReturn {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const [pageSize] = useState(initialPageSize);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [filters, setFilters] = useState<FilterState>({});
  const [queryParam, setQueryParam] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize state from URL parameters on mount
  useEffect(() => {
    if (!isInitialized) {
      const urlFilters: FilterState = {};
      const urlSearchTerm = searchParams.get('search') || '';
      const urlPage = parseInt(searchParams.get('page') || '1');

      // Extract all URL parameters as filters
      searchParams.forEach((value, key) => {
        if (key !== 'search' && key !== 'page' && key !== 'limit') {
          // Handle different data types
          if (value === 'true' || value === 'false') {
            urlFilters[key] = value === 'true';
          } else if (!isNaN(Number(value)) && value !== '') {
            urlFilters[key] = Number(value);
          } else if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
            // Date format YYYY-MM-DD
            urlFilters[key] = new Date(value);
          } else {
            urlFilters[key] = value;
          }
        }
      });

      setSearchTerm(urlSearchTerm);
      setDebouncedSearchTerm(urlSearchTerm);
      setCurrentPage(urlPage);
      setFilters(urlFilters);
      setIsInitialized(true);
    }
  }, [searchParams, isInitialized]);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceDelay);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceDelay]);

  // Build query parameters for API calls
  useEffect(() => {
    const params: string[] = [];

    if (debouncedSearchTerm) {
      params.push(`search=${encodeURIComponent(debouncedSearchTerm)}`);
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '' && value !== 'all') {
        if (value instanceof Date) {
          params.push(`${key}=${value.toISOString().split('T')[0]}`);
        } else {
          params.push(`${key}=${encodeURIComponent(String(value))}`);
        }
      }
    });

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, filters]);

  // Update URL with current state
  const updateUrl = useCallback((newFilters?: Partial<FilterState>, resetPage = false) => {
    const params = new URLSearchParams();
    
    // Add search parameter
    if (searchTerm) {
      params.set('search', searchTerm);
    }
    
    // Add page parameter (reset to 1 if resetPage is true)
    const pageToSet = resetPage ? 1 : currentPage;
    if (pageToSet > 1) {
      params.set('page', pageToSet.toString());
    }
    
    // Add filter parameters
    const filtersToUse = newFilters ? { ...filters, ...newFilters } : filters;
    Object.entries(filtersToUse).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '' && value !== 'all') {
        if (value instanceof Date) {
          params.set(key, value.toISOString().split('T')[0]);
        } else {
          params.set(key, String(value));
        }
      }
    });

    const newUrl = params.toString() ? `${pathname}?${params.toString()}` : pathname;
    router.replace(newUrl, { scroll: false });
    
    if (resetPage && currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [router, pathname, searchTerm, currentPage, filters]);

  // Update URL when search term changes (with debounce effect)
  useEffect(() => {
    if (isInitialized) {
      updateUrl(undefined, true); // Reset page when search changes
    }
  }, [debouncedSearchTerm, isInitialized]);

  // Filter management functions
  const updateFilter = useCallback((key: string, value: string | number | boolean | Date | undefined | null) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    updateUrl(newFilters, true); // Reset page when filter changes
  }, [filters, updateUrl]);

  const updateFilters = useCallback((newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    updateUrl(updatedFilters, true); // Reset page when filters change
  }, [filters, updateUrl]);

  const clearFilter = useCallback((key: string) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    setFilters(newFilters);
    updateUrl(newFilters, true);
  }, [filters, updateUrl]);

  const clearAllFilters = useCallback(() => {
    setFilters({});
    setSearchTerm('');
    setCurrentPage(1);
    router.replace(pathname, { scroll: false });
  }, [router, pathname]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  }, []);

  const handlePageChange = useCallback((pageNumber: number) => {
    setCurrentPage(pageNumber);
    const params = new URLSearchParams();
    
    if (searchTerm) {
      params.set('search', searchTerm);
    }
    
    if (pageNumber > 1) {
      params.set('page', pageNumber.toString());
    }
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '' && value !== 'all') {
        if (value instanceof Date) {
          params.set(key, value.toISOString().split('T')[0]);
        } else {
          params.set(key, String(value));
        }
      }
    });

    const newUrl = params.toString() ? `${pathname}?${params.toString()}` : pathname;
    router.replace(newUrl, { scroll: false });
  }, [router, pathname, searchTerm, filters]);

  return {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    filters,
    updateFilter,
    updateFilters,
    clearFilter,
    clearAllFilters,
    queryParam,
    updateUrl,
  };
}
