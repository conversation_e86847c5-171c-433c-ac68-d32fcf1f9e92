// components/ui/ProfileEditor.tsx
import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  <PERSON>er,
  Drawer<PERSON>ontent,
  DrawerFooter,
  <PERSON>er<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
  DrawerDescription,
  DrawerClose,
} from '@/components/ui/drawer';
import { Loader2 } from 'lucide-react';
import useMediaQuery from '@/hooks/mediaQuery';

interface ProfileEditorProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
  description: string;
  onSubmit?: () => void;
  isLoading?: boolean;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export const Modal = ({
  open,
  setOpen,
  title,
  description,
  isLoading,
  onSubmit,
  children,
  size = 'md',
}: ProfileEditorProps) => {
  const [isDesktop, setIsDesktop] = React.useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth >= 480;
    }
    return true;
  });

  React.useEffect(() => {
    if (!open) return;

    const checkDesktop = () => {
      const newIsDesktop = window.innerWidth >= 480;
      if (newIsDesktop !== isDesktop) {
        setIsDesktop(newIsDesktop);
      }
    };

    window.addEventListener('resize', checkDesktop);
    return () => window.removeEventListener('resize', checkDesktop);
  }, [open, isDesktop]);

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent
          onInteractOutside={(e) => {
            // Prevent closing when clicking inside the modal
            e.preventDefault();
          }}
          className={`${
            size === 'sm'
              ? 'max-w-sm'
              : size === 'lg'
                ? 'max-w-3xl'
                : size === 'xl'
                  ? 'max-w-5xl'
                  : 'max-w-md'
          }`}
        >
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription>{description}</DialogDescription>
          </DialogHeader>
          <div className="overflow-auto max-h-100">{children}</div>
          <DialogFooter>
            {onSubmit && (
              <Button disabled={isLoading} onClick={onSubmit}>
                {isLoading ? <Loader2 className="animate-spin" /> : 'Submit'}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent
        onInteractOutside={(e) => {
          // Prevent closing when clicking inside the drawer
          e.preventDefault();
        }}
      >
        <DrawerHeader className="text-left">
          <DrawerTitle>{title}</DrawerTitle>
          <DrawerDescription>{description}</DrawerDescription>
        </DrawerHeader>
        <div className="px-4 py-2 overflow-auto">{children}</div>
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            {/* <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button> */}
          </DrawerClose>
          {onSubmit && (
            <Button disabled={isLoading} onClick={onSubmit}>
              {isLoading ? <Loader2 className="animate-spin" /> : 'Submit'}
            </Button>
          )}
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};
